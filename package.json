{"name": "gemini-blog-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@google/genai": "^1.11.0", "@supabase/supabase-js": "^2.52.1", "@tailwindcss/vite": "^4.1.11", "browser-image-compression": "^2.0.2", "dotenv": "^17.2.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-is": "^19.1.0", "react-router-dom": "^7.7.1", "recharts": "^3.1.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "typescript": "~5.7.2", "vite": "^6.2.0"}}